<template>
  <!-- 右侧滑出的打印标签抽屉 -->
  <el-drawer
    v-model="visible"
    title="打印标签预览"
    direction="rtl"
    size="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <!-- 工具栏 -->
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">打印标签预览</span>
        <div class="drawer-actions">
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownload"
            :loading="downloading"
            size="small"
          >
            下载
          </el-button>
        </div>
      </div>
    </template>

    <!-- 打印标签内容 -->
    <div class="print-content" v-loading="loading">
      <div class="print-info-bar">
        <el-alert
          :title="`共 ${printData?.totalCount || 0} 条记录待打印`"
          type="info"
          :closable="false"
          show-icon
        />

        <!-- 预览工具栏 -->
        <div class="preview-toolbar">
          <el-button-group size="small">
            <el-button @click="handleZoomOut" :disabled="currentZoom <= 0.5">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="handleResetZoom">
              {{ Math.round(currentZoom * 100) }}%
            </el-button>
            <el-button @click="handleZoomIn" :disabled="currentZoom >= 3.0">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>

          <el-button size="small" @click="handleRefreshPreview">
            <el-icon><Refresh /></el-icon>
            刷新预览
          </el-button>
        </div>
      </div>

      <div class="print-page">
        <!-- Hiprint专业预览 -->
        <div class="hiprint-preview-container">
          <div
            id="hiprint-preview-area"
            class="hiprint-preview-area"
            :style="{ transform: `scale(${currentZoom})`, transformOrigin: 'top left' }"
          >
            <!-- hiprint预览内容将在这里渲染 -->
          </div>

          <!-- 加载状态 -->
          <div v-if="hiprintLoading" class="hiprint-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在加载预览...</span>
          </div>

          <!-- 错误状态 -->
          <div v-if="hiprintError" class="hiprint-error">
            <el-icon><Warning /></el-icon>
            <div class="error-content">
              <p>预览加载失败</p>
              <p class="error-message">{{ hiprintError }}</p>
              <el-button size="small" type="primary" @click="handleRetryPreview">
                重试
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
  import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    Printer,
    Download,
    ZoomIn,
    ZoomOut,
    Refresh,
    Loading,
    Warning
  } from '@element-plus/icons-vue';

  // hiprint相关导入
  let hiprintModule = null;
  let hiprintTemplate = null;

  // 调试工具导入
  import { fullHiprintDebug, debugTemplateData, debugPrintData } from '../utils/hiprint-debug.js';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    printData: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits(['update:modelValue', 'confirm-print']);

  // 响应式数据
  const loading = ref(false);
  const printing = ref(false);
  const downloading = ref(false);
  const hiprintLoading = ref(false);
  const hiprintError = ref('');
  const currentZoom = ref(1.0);
  const retryCount = ref(0);
  const maxRetryCount = 3;

  // 计算属性
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  // 处理打印
  const handlePrint = async () => {
    printing.value = true;
    try {
      // 使用浏览器打印
      window.print();
      emit('confirm-print', {
        ...props.printData,
        action: 'print'
      });
    } catch (error) {
      EleMessage.error('打印失败：' + error.message);
    } finally {
      printing.value = false;
    }
  };

  // 处理下载
  const handleDownload = async () => {
    downloading.value = true;
    try {
      emit('confirm-print', {
        ...props.printData,
        action: 'download'
      });
    } catch (error) {
      EleMessage.error('下载失败：' + error.message);
    } finally {
      downloading.value = false;
    }
  };

  // 缩放控制
  const handleZoomIn = () => {
    if (currentZoom.value < 3.0) {
      currentZoom.value = Math.min(3.0, currentZoom.value + 0.1);
    }
  };

  const handleZoomOut = () => {
    if (currentZoom.value > 0.5) {
      currentZoom.value = Math.max(0.5, currentZoom.value - 0.1);
    }
  };

  const handleResetZoom = () => {
    currentZoom.value = 1.0;
  };

  // 刷新预览
  const handleRefreshPreview = async () => {
    await initHiprintPreview();
  };

  // 重试预览
  const handleRetryPreview = async () => {
    if (retryCount.value < maxRetryCount) {
      retryCount.value++;
      hiprintError.value = '';
      await initHiprintPreview();
    } else {
      EleMessage.error('预览加载失败次数过多，请检查网络连接或联系管理员');
    }
  };

  // 初始化hiprint预览
  const initHiprintPreview = async () => {
    if (!props.printData) {
      console.warn('printData为空，无法初始化预览');
      return;
    }

    try {
      hiprintLoading.value = true;
      hiprintError.value = '';

      console.log('开始初始化hiprint预览，数据:', props.printData);

      // 按需加载hiprint模块
      await loadHiprintModule();

      // 转换数据格式并创建模板
      const templateData = convertToHiprintFormat(props.printData);
      const printDataList = preparePrintData(props.printData);

      // 运行完整调试
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 运行hiprint调试...');
        const debugResult = await fullHiprintDebug(templateData, printDataList, 'hiprint-preview-area');
        if (!debugResult.overall) {
          console.error('🔍 调试发现问题，但继续尝试渲染...');
        }
      }

      // 等待DOM更新
      await nextTick();

      const previewContainer = document.getElementById('hiprint-preview-area');
      if (!previewContainer) {
        throw new Error('hiprint预览容器不存在');
      }

      // 清空容器
      previewContainer.innerHTML = '';

      console.log('转换后的模板数据:', templateData);

      // 验证模板数据
      if (!validateTemplateData(templateData)) {
        throw new Error('模板数据格式无效');
      }

      // 创建hiprint模板实例
      hiprintTemplate = createHiprintTemplate(templateData);
      console.log('hiprint模板实例创建成功:', hiprintTemplate);

      console.log('准备的打印数据:', printDataList);

      if (!printDataList || printDataList.length === 0) {
        throw new Error('打印数据为空');
      }

      // 渲染预览 - 使用hiprint的正确API
      console.log('开始渲染hiprint预览...');

      try {
        // 方法1: 使用print方法直接渲染到容器
        const printOptions = {
          preview: true,
          width: 'auto',
          height: 'auto',
          callback: (html) => {
            console.log('hiprint渲染回调，HTML长度:', html?.length || 0);
            if (html) {
              previewContainer.innerHTML = html;
            }
          }
        };

        // 调用hiprint的print方法
        const printResult = hiprintTemplate.print(printDataList, printOptions);
        console.log('hiprint print方法返回结果:', printResult);

        // 如果print方法直接返回了HTML内容
        if (typeof printResult === 'string' && printResult.length > 0) {
          previewContainer.innerHTML = printResult;
        } else if (printResult && printResult.length > 0) {
          // 如果返回的是DOM元素数组
          previewContainer.innerHTML = '';
          printResult.forEach(element => {
            if (element instanceof HTMLElement) {
              previewContainer.appendChild(element);
            } else if (typeof element === 'string') {
              previewContainer.innerHTML += element;
            }
          });
        } else {
          // 方法2: 使用print2方法（如果存在）
          if (typeof hiprintTemplate.print2 === 'function') {
            console.log('尝试使用print2方法...');
            hiprintTemplate.print2(printDataList, {
              callback: (html) => {
                console.log('print2回调，HTML长度:', html?.length || 0);
                if (html) {
                  previewContainer.innerHTML = html;
                } else {
                  throw new Error('print2方法返回空内容');
                }
              },
              preview: true
            });
          } else {
            throw new Error('hiprint渲染方法调用失败，无可用的渲染方法');
          }
        }

        // 等待一小段时间让渲染完成
        await new Promise(resolve => setTimeout(resolve, 500));

        // 检查渲染结果
        if (previewContainer.innerHTML.trim().length === 0) {
          throw new Error('hiprint渲染完成但容器仍为空');
        }

      } catch (renderError) {
        console.error('hiprint渲染过程出错:', renderError);
        throw renderError;
      }

      console.log('hiprint预览渲染成功');
      retryCount.value = 0; // 重置重试计数

    } catch (error) {
      console.error('hiprint预览初始化失败:', error);
      hiprintError.value = error.message || '预览加载失败';

      // 详细错误日志
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        printData: props.printData,
        hiprintModule: !!hiprintModule,
        hiprintTemplate: !!hiprintTemplate
      });

      // 如果是首次加载失败，自动重试一次
      if (retryCount.value === 0) {
        retryCount.value++;
        console.log('首次加载失败，1秒后重试...');
        setTimeout(() => {
          initHiprintPreview();
        }, 1000);
      }
    } finally {
      hiprintLoading.value = false;
    }
  };

  // 按需加载hiprint模块
  const loadHiprintModule = async () => {
    if (hiprintModule) {
      return hiprintModule;
    }

    try {
      // 动态导入hiprint配置
      hiprintModule = await import('@/utils/hiprint-config');

      // 初始化hiprint
      await hiprintModule.initHiprint();

      console.log('hiprint模块加载成功');
      return hiprintModule;
    } catch (error) {
      console.error('hiprint模块加载失败:', error);
      throw new Error('hiprint模块加载失败，请检查依赖是否正确安装');
    }
  };

  // 验证模板数据格式
  const validateTemplateData = (templateData) => {
    if (!templateData || typeof templateData !== 'object') {
      console.error('模板数据无效: 不是对象', templateData);
      return false;
    }

    if (!templateData.panels || !Array.isArray(templateData.panels)) {
      console.error('模板数据无效: 缺少panels数组', templateData);
      return false;
    }

    if (templateData.panels.length === 0) {
      console.error('模板数据无效: panels数组为空', templateData);
      return false;
    }

    // 检查每个面板
    for (const panel of templateData.panels) {
      if (!panel.printElements || !Array.isArray(panel.printElements)) {
        console.error('面板数据无效: 缺少printElements数组', panel);
        return false;
      }
    }

    console.log('模板数据验证通过');
    return true;
  };

  // 创建hiprint模板实例
  const createHiprintTemplate = (templateData) => {
    if (!hiprintModule) {
      throw new Error('hiprint模块未加载');
    }

    try {
      console.log('使用hiprint配置创建模板，数据:', templateData);

      // 使用hiprint配置文件中的方法创建模板
      const template = hiprintModule.createPrintTemplate(templateData, {
        settingContainer: null, // 预览模式不需要属性面板
        paginationContainer: null,
        dataMode: 1,
        history: false // 预览模式不需要历史记录
      });

      console.log('hiprint模板实例创建成功:', template);

      // 验证模板是否正确创建
      if (!template) {
        throw new Error('模板创建失败，返回null');
      }

      // 检查模板是否有面板
      if (!template.panels || template.panels.length === 0) {
        console.warn('模板创建后没有面板，模板数据可能有问题');
      } else {
        console.log('模板面板数量:', template.panels.length);
      }

      return template;
    } catch (error) {
      console.error('创建hiprint模板失败:', error);
      throw new Error('创建打印模板失败：' + error.message);
    }
  };

  // 将打印数据转换为hiprint格式
  const convertToHiprintFormat = (printData) => {
    try {
      // 如果已经是hiprint格式，需要调整为多页布局
      if (printData.layoutConfig && typeof printData.layoutConfig === 'object') {
        return adjustTemplateForMultiPage(printData.layoutConfig, printData);
      }

      // 如果是字符串，尝试解析后调整
      if (typeof printData.layoutConfig === 'string') {
        const parsedConfig = JSON.parse(printData.layoutConfig);
        return adjustTemplateForMultiPage(parsedConfig, printData);
      }

      // 否则根据模板类型创建默认模板
      return createDefaultTemplate(printData);
    } catch (error) {
      console.error('转换hiprint格式失败:', error);
      return createDefaultTemplate(printData);
    }
  };

  // 调整自定义模板为多页布局
  const adjustTemplateForMultiPage = (templateConfig, printData) => {
    console.log('调整自定义模板为多页布局:', templateConfig);
    console.log('当前打印数据标签类型:', printData.labelType);

    // 如果模板已经是多页布局，需要检查标签类型是否匹配
    if (templateConfig.panels && templateConfig.panels.length > 0) {
      const panel = templateConfig.panels[0];
      const labelType = printData.labelType || 2; // 默认大签

      // 标签尺寸配置
      const labelSizes = {
        1: { width: 191, height: 26 },  // 标准标签
        2: { width: 119, height: 30 },  // 大签标签
        3: { width: 160, height: 22 }   // 小标签
      };

      const paperSizes = {
        1: { width: 210, height: 297 },
        2: { width: 192, height: 290 },
        3: { width: 210, height: 297 }
      };

      const expectedLabelSize = labelSizes[labelType];
      const paperSize = paperSizes[labelType] || paperSizes[2];

      // 检查模板的标签类型是否匹配当前需要的标签类型
      const isLabelSizeMatch = Math.abs(panel.width - expectedLabelSize.width) < 10 &&
                              Math.abs(panel.height - expectedLabelSize.height) < 10;

      // 检查是否已经是纸张尺寸（多页布局）
      const isPaperSizeMatch = Math.abs(panel.width - paperSize.width) < 10 &&
                              Math.abs(panel.height - paperSize.height) < 10;

      if (isPaperSizeMatch) {
        console.log('模板已经是多页布局，直接使用');
        return templateConfig;
      } else if (isLabelSizeMatch) {
        console.log('模板标签类型匹配，转换为多页布局');
        return convertSingleLabelToMultiPage(templateConfig, printData);
      } else {
        console.warn(`模板标签尺寸(${panel.width}x${panel.height})与当前标签类型${labelType}(${expectedLabelSize.width}x${expectedLabelSize.height})不匹配，使用默认模板`);
        return createDefaultTemplate(printData);
      }
    }

    return templateConfig;
  };

  // 转换单标签模板为多页布局
  const convertSingleLabelToMultiPage = (singleLabelTemplate, printData) => {
    const labelType = printData.labelType || 2;
    const labelSizes = {
      1: { width: 191, height: 26 },
      2: { width: 119, height: 30 }, // 大签标签 - 增加高度以容纳更多内容
      3: { width: 160, height: 22 }
    };
    const paperSizes = {
      1: { width: 210, height: 297 },
      2: { width: 192, height: 290 }, // 大签专用纸张
      3: { width: 210, height: 297 }
    };

    const labelSize = labelSizes[labelType] || labelSizes[2];
    const paperSize = paperSizes[labelType] || paperSizes[2];
    const layout = calculateLabelLayout(labelSize, paperSize);

    console.log('转换单标签模板，标签尺寸:', labelSize, '纸张尺寸:', paperSize);

    // 获取原始模板的打印元素
    const originalPanel = singleLabelTemplate.panels[0];
    const originalElements = originalPanel.printElements || [];

    console.log('原始模板元素数量:', originalElements.length);

    // 创建多页布局的打印元素
    const multiPageElements = [];

    // 为每个标签位置复制原始元素
    for (let row = 0; row < layout.rowsPerPage; row++) {
      for (let col = 0; col < layout.labelsPerRow; col++) {
        const labelIndex = row * layout.labelsPerRow + col;

        // 计算标签在页面上的位置
        const labelX = layout.margin + col * (labelSize.width + layout.spacing);
        const labelY = layout.margin + row * (labelSize.height + layout.spacing);

        // 复制并调整每个原始元素
        originalElements.forEach(element => {
          const newElement = JSON.parse(JSON.stringify(element)); // 深拷贝

          // 调整位置
          newElement.options.left = labelX + (element.options.left || 0);
          newElement.options.top = labelY + (element.options.top || 0);

          // 调整字段引用，使用数组索引
          if (newElement.options.field) {
            newElement.options.field = `items.${labelIndex}.${newElement.options.field}`;
          }

          multiPageElements.push(newElement);
        });
      }
    }

    console.log('生成多页元素数量:', multiPageElements.length);

    // 创建新的多页模板
    return {
      panels: [{
        index: 0,
        name: originalPanel.name + '(多页布局)',
        height: paperSize.height,
        width: paperSize.width,
        paperHeader: 5,
        paperFooter: 5,
        printElements: multiPageElements
      }]
    };
  };

  // 创建默认模板（用于系统预定义模板）
  const createDefaultTemplate = (printData) => {
    const templateType = printData.templateType || 'SYSTEM';
    const labelType = printData.labelType || 1;

    // 根据标签类型设置尺寸（单位：mm）
    const labelSizes = {
      1: { width: 191, height: 26 }, // 标准标签
      2: { width: 119, height: 30 }, // 大签标签 - 增加高度以容纳更多内容
      3: { width: 160, height: 22 }  // 小标签
    };

    // 纸张尺寸配置
    const paperSizes = {
      1: { width: 210, height: 297 }, // A4纸张
      2: { width: 192, height: 290 }, // 大签专用纸张
      3: { width: 210, height: 297 }  // A4纸张
    };

    const labelSize = labelSizes[labelType] || labelSizes[1];
    const paperSize = paperSizes[labelType] || paperSizes[1];

    console.log(`创建默认模板: ${templateType}, 标签类型: ${labelType}`);
    console.log('标签尺寸:', labelSize);
    console.log('纸张尺寸:', paperSize);

    // 计算布局信息
    const layout = calculateLabelLayout(labelSize, paperSize);
    console.log('标签布局:', layout);

    // 获取实际数据数量
    const actualDataCount = printData.items ? printData.items.length : 0;
    console.log(`实际数据数量: ${actualDataCount}条`);

    // 计算需要的页数
    const totalPages = Math.ceil(actualDataCount / layout.labelsPerPage);
    console.log(`需要页数: ${totalPages}页`);

    // 创建多页模板
    const panels = [];
    for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      const pageStartIndex = pageIndex * layout.labelsPerPage;
      const pageEndIndex = Math.min(pageStartIndex + layout.labelsPerPage, actualDataCount);
      const pageDataCount = pageEndIndex - pageStartIndex;

      console.log(`第${pageIndex + 1}页: 数据索引${pageStartIndex}-${pageEndIndex - 1}, 共${pageDataCount}条`);

      panels.push({
        index: pageIndex,
        name: `${templateType}标签模板-第${pageIndex + 1}页`,
        height: paperSize.height,
        width: paperSize.width,
        paperHeader: 5,
        paperFooter: 5,
        printElements: createPagePrintElements(labelSize, layout, pageStartIndex, pageDataCount)
      });
    }

    const template = {
      panels: panels
    };

    console.log(`默认模板创建完成: ${panels.length}个面板`);
    return template;
  };

  // 创建单页的打印元素
  const createPagePrintElements = (labelSize, layout, pageStartIndex, pageDataCount) => {
    const elements = [];
    const { labelsPerRow, rowsPerPage, margin, spacing } = layout;

    console.log(`创建第${Math.floor(pageStartIndex / layout.labelsPerPage) + 1}页打印元素，数据起始索引: ${pageStartIndex}, 数据数量: ${pageDataCount}`);

    // 为每个标签位置创建打印元素
    for (let row = 0; row < rowsPerPage; row++) {
      for (let col = 0; col < labelsPerRow; col++) {
        const positionIndex = row * labelsPerRow + col;
        const dataIndex = pageStartIndex + positionIndex;

        // 只为有数据的位置创建元素
        if (positionIndex < pageDataCount) {
          // 计算标签在页面上的位置
          const labelX = margin + col * (labelSize.width + spacing);
          const labelY = margin + row * (labelSize.height + spacing);

          console.log(`位置${positionIndex}: 数据索引${dataIndex}, 坐标(${labelX}, ${labelY})`);

          // 为该位置创建打印元素
          const labelElements = createSingleLabelElements(
            labelX,
            labelY,
            labelSize,
            dataIndex  // 使用全局数据索引
          );

          elements.push(...labelElements);
        }
      }
    }

    console.log(`页面打印元素创建完成: ${elements.length}个元素`);
    return elements;
  };

  // 计算标签在纸张上的布局
  const calculateLabelLayout = (labelSize, paperSize) => {
    const margin = 5; // 页面边距 (mm)
    const spacing = 2; // 标签间距 (mm)

    // 可用打印区域
    const printableWidth = paperSize.width - (margin * 2);
    const printableHeight = paperSize.height - (margin * 2);

    // 计算每行可放多少个标签
    const labelsPerRow = Math.floor((printableWidth + spacing) / (labelSize.width + spacing));

    // 计算每列可放多少行标签
    const rowsPerPage = Math.floor((printableHeight + spacing) / (labelSize.height + spacing));

    // 每页总标签数
    const labelsPerPage = labelsPerRow * rowsPerPage;

    console.log(`布局计算: ${labelsPerRow}列 × ${rowsPerPage}行 = ${labelsPerPage}个标签/页`);

    return {
      labelsPerRow,
      rowsPerPage,
      labelsPerPage,
      margin,
      spacing,
      printableWidth,
      printableHeight
    };
  };



  // 创建单个标签的打印元素（大签标签格式）
  const createSingleLabelElements = (startX, startY, labelSize, dataIndex) => {
    const elements = [];

    console.log(`创建大签标签元素，位置(${startX}, ${startY})，数据索引: ${dataIndex}`);

    // 银行名称（顶部居中，大字体）
    elements.push({
      options: {
        left: startX + 2,
        top: startY + 2,
        height: 8,
        width: labelSize.width - 4,
        title: '银行名称',
        field: `items.${dataIndex}.bankName`,
        fontSize: 14,
        fontWeight: 'bold',
        textAlign: 'center',
        color: '#000000',
        backgroundColor: 'rgba(255,255,255,0)'
      },
      printElementType: {
        title: '文本',
        type: 'text'
      }
    });

    // 钱币详细信息（第二行，左侧）
    elements.push({
      options: {
        left: startX + 2,
        top: startY + 12,
        height: 7,
        width: 70,
        title: '钱币信息',
        field: `items.${dataIndex}.coinName`,
        fontSize: 11,
        fontWeight: 'normal',
        textAlign: 'left',
        color: '#000000',
        backgroundColor: 'rgba(255,255,255,0)'
      },
      printElementType: {
        title: '文本',
        type: 'text'
      }
    });

    // 大号评级分数（右侧）
    elements.push({
      options: {
        left: startX + 75,
        top: startY + 9,
        height: 14,
        width: 25,
        title: '评级分数',
        field: `items.${dataIndex}.gradeScore`,
        fontSize: 26,
        fontWeight: 'bold',
        textAlign: 'center',
        color: '#000000',
        backgroundColor: 'rgba(255,255,255,0)'
      },
      printElementType: {
        title: '文本',
        type: 'text'
      }
    });

    // 评级等级文字（分数下方）
    elements.push({
      options: {
        left: startX + 72,
        top: startY + 21,
        height: 4,
        width: 30,
        title: '评级等级',
        field: `items.${dataIndex}.gradeLevel`,
        fontSize: 9,
        fontWeight: 'normal',
        textAlign: 'center',
        color: '#000000',
        backgroundColor: 'rgba(255,255,255,0)'
      },
      printElementType: {
        title: '文本',
        type: 'text'
      }
    });

    // 星级评分（装饰性元素）
    elements.push({
      options: {
        left: startX + 72,
        top: startY + 25,
        height: 3,
        width: 30,
        title: '星级',
        field: '', // 固定显示星级
        fontSize: 8,
        fontWeight: 'normal',
        textAlign: 'center',
        color: '#FFD700',
        backgroundColor: 'rgba(255,255,255,0)',
        defaultValue: '★★★★★'
      },
      printElementType: {
        title: '文本',
        type: 'text'
      }
    });

    // 序列号信息（第三行左侧）
    elements.push({
      options: {
        left: startX + 2,
        top: startY + 21,
        height: 5,
        width: 65,
        title: '序列号',
        field: `items.${dataIndex}.serialNumber`,
        fontSize: 9,
        fontWeight: 'normal',
        textAlign: 'left',
        color: '#000000',
        backgroundColor: 'rgba(255,255,255,0)'
      },
      printElementType: {
        title: '文本',
        type: 'text'
      }
    });

    // 二维码（右上角）
    elements.push({
      options: {
        left: startX + labelSize.width - 20,
        top: startY + 2,
        height: 18,
        width: 18,
        title: '二维码',
        field: `items.${dataIndex}.diyCode`,
        fontSize: 6,
        fontWeight: 'normal',
        textAlign: 'center',
        color: '#000000',
        backgroundColor: 'rgba(255,255,255,0)'
      },
      printElementType: {
        title: '二维码',
        type: 'qrcode'
      }
    });

    // 送评条码（底部）
    elements.push({
      options: {
        left: startX + labelSize.width - 20,
        top: startY + 22,
        height: 6,
        width: 18,
        title: '条码',
        field: `items.${dataIndex}.diyCode`,
        fontSize: 7,
        fontWeight: 'normal',
        textAlign: 'center',
        color: '#000000',
        backgroundColor: 'rgba(255,255,255,0)'
      },
      printElementType: {
        title: '文本',
        type: 'text'
      }
    });

    return elements;
  };



  // 准备打印数据（支持多页分组）
  const preparePrintData = (printData) => {
    if (!printData.items || !Array.isArray(printData.items)) {
      console.warn('printData.items不存在或不是数组:', printData);
      return [];
    }

    // 获取标签和纸张尺寸信息
    const labelType = printData.labelType || 1;

    // 标签尺寸配置（单位：mm）
    const labelSizes = {
      1: { width: 191, height: 26 },  // 标准标签
      2: { width: 119, height: 30 },  // 大签标签 - 增加高度以容纳更多内容
      3: { width: 160, height: 22 }   // 小标签
    };

    // 纸张尺寸配置（单位：mm）
    const paperSizes = {
      1: { width: 210, height: 297 },  // A4纸张
      2: { width: 192, height: 290 },  // 大签专用纸张
      3: { width: 210, height: 297 }   // A4纸张
    };

    // 获取当前标签和纸张尺寸
    const labelSize = labelSizes[labelType] || labelSizes[1];
    const paperSize = paperSizes[labelType] || paperSizes[1];

    // 计算布局信息
    const layout = calculateLabelLayout(labelSize, paperSize);

    console.log('标签尺寸:', labelSize);
    console.log('纸张尺寸:', paperSize);
    console.log('布局信息:', layout);
    console.log(`每页可放标签数: ${layout.labelsPerPage}个`);

    // 准备基础数据
    const items = printData.items.map((item, index) => ({
      // 基础信息
      bankName: item.bankName || '',
      coinName: item.coinName || item.name || '',
      gradeScore: item.gradeScore || item.score || '',

      // 扩展信息
      gradeLevel: item.gradeLevel || '',
      diyCode: item.diyCode || '',
      customerName: item.customerName || '',
      yearInfo: item.yearInfo || '',
      serialNumber: item.serialNumber || '',
      version: item.version || '',

      // 保留原始数据
      ...item,

      // 添加索引
      _index: index
    }));

    // 计算总页数
    const totalPages = Math.ceil(items.length / layout.labelsPerPage);
    console.log(`总共${items.length}条记录，需要${totalPages}页`);

    // 返回单一数据对象，包含所有必要信息
    const result = {
      items: items,           // 所有数据项
      totalCount: items.length,
      totalPages: totalPages,
      layout: layout,         // 布局信息
      labelType: printData.labelType,
      templateType: printData.templateType
    };

    console.log(`准备的打印数据: ${items.length}条记录，需要${totalPages}页`);
    return result;
  };

  // 清理资源
  const cleanup = () => {
    if (hiprintTemplate) {
      try {
        // 清理hiprint模板实例
        if (hiprintModule && hiprintModule.cleanupTemplate) {
          hiprintModule.cleanupTemplate(hiprintTemplate);
        }
      } catch (error) {
        console.warn('清理hiprint模板失败:', error);
      }
      hiprintTemplate = null;
    }
  };

  // 监听打印数据变化，重新渲染预览
  watch(() => props.printData, (newData) => {
    if (newData && visible.value) {
      initHiprintPreview();
    }
  }, { immediate: false });

  // 监听抽屉显示状态
  watch(visible, (isVisible) => {
    if (isVisible && props.printData) {
      // 延迟初始化，确保DOM已渲染
      nextTick(() => {
        initHiprintPreview();
      });
    } else if (!isVisible) {
      // 关闭时清理资源
      cleanup();
    }
  });

  // 组件挂载时初始化
  onMounted(() => {
    console.log('print-labels组件已挂载');
  });

  // 组件卸载时清理资源
  onUnmounted(() => {
    cleanup();
    console.log('print-labels组件已卸载');
  });

</script>



<style scoped>
  /* 抽屉头部样式 */
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .drawer-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .drawer-actions {
    display: flex;
    gap: 8px;
  }

  /* 打印内容样式 */
  .print-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .print-info-bar {
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  .preview-toolbar {
    margin-top: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
  }

  .print-page {
    flex: 1;
    overflow: auto;
    padding: 16px;
    background: #f5f5f5;
  }

  /* hiprint预览容器样式 */
  .hiprint-preview-container {
    width: 100%;
    min-height: 400px;
    position: relative;
  }

  .hiprint-preview-area {
    width: 100%;
    min-height: 400px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: white;
    padding: 20px;
    overflow: auto;
    transform-origin: top left;
    transition: transform 0.3s ease;
  }

  /* 加载状态样式 */
  .hiprint-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #606266;
    font-size: 14px;
  }

  .hiprint-loading .el-icon {
    font-size: 24px;
  }

  /* 错误状态样式 */
  .hiprint-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: #f56c6c;
    text-align: center;
  }

  .hiprint-error .el-icon {
    font-size: 48px;
  }

  .error-content p {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
  }

  .error-message {
    font-size: 14px !important;
    color: #909399 !important;
    font-weight: normal !important;
  }

  /* hiprint专用样式 */
  :deep(.hiprint-printPaper) {
    margin: 10px auto !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  :deep(.hiprint-printPaper .hiprint-printElement) {
    border: none !important;
  }

  :deep(.hiprint-printPaper .hiprint-printElement-text) {
    line-height: 1.2 !important;
    word-break: break-all !important;
  }

  /* 打印媒体查询 - 优化浏览器打印效果 */
  @media print {
    /* 隐藏不需要打印的元素 */
    .drawer-header,
    .print-info-bar,
    .preview-toolbar,
    .hiprint-loading,
    .hiprint-error {
      display: none !important;
    }

    /* 重置打印容器样式 */
    .print-content {
      height: auto !important;
      background: white !important;
    }

    .print-page {
      padding: 0 !important;
      background: white !important;
      overflow: visible !important;
    }

    .hiprint-preview-container {
      width: 100% !important;
      min-height: auto !important;
      background: white !important;
    }

    .hiprint-preview-area {
      border: none !important;
      padding: 0 !important;
      background: white !important;
      transform: none !important;
      overflow: visible !important;
    }

    /* hiprint打印优化 */
    :deep(.hiprint-printPaper) {
      margin: 0 !important;
      box-shadow: none !important;
      page-break-inside: avoid !important;
      background: white !important;
    }

    :deep(.hiprint-printElement) {
      border: none !important;
      background: transparent !important;
    }

    :deep(.hiprint-printElement-text) {
      color: black !important;
      background: transparent !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }

    /* 页面设置 */
    @page {
      margin: 10mm;
      size: A4;
    }

    /* 标签间距 */
    :deep(.hiprint-printPaper + .hiprint-printPaper) {
      margin-top: 5mm !important;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .preview-toolbar {
      flex-direction: column;
      gap: 8px;
    }

    .hiprint-preview-area {
      padding: 10px;
    }
  }
</style>
