<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .layout-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .label-preview {
            border: 2px solid #ddd;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        .label {
            border: 1px solid #333;
            margin: 2px;
            padding: 2mm;
            display: inline-block;
            vertical-align: top;
            background: #fafafa;
            position: relative;
            font-size: 10px;
        }
        .label-content {
            height: 100%;
            position: relative;
        }
        .bank-name {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 2mm;
        }
        .coin-info {
            font-size: 11px;
            margin-bottom: 1mm;
        }
        .grade-score {
            position: absolute;
            right: 20px;
            top: 8mm;
            font-size: 26px;
            font-weight: bold;
            text-align: center;
            width: 25px;
        }
        .grade-level {
            position: absolute;
            right: 15px;
            top: 21mm;
            font-size: 9px;
            text-align: center;
            width: 30px;
        }
        .stars {
            position: absolute;
            right: 15px;
            top: 25mm;
            font-size: 8px;
            color: #FFD700;
            width: 30px;
            text-align: center;
        }
        .serial-number {
            position: absolute;
            left: 2px;
            top: 21mm;
            font-size: 9px;
            width: 65px;
        }
        .qr-code {
            position: absolute;
            right: 2px;
            top: 2mm;
            width: 18px;
            height: 18px;
            border: 1px solid #ccc;
            font-size: 6px;
            text-align: center;
            line-height: 18px;
        }
        .barcode {
            position: absolute;
            right: 2px;
            top: 22mm;
            width: 18px;
            height: 6px;
            border: 1px solid #ccc;
            font-size: 7px;
            text-align: center;
            line-height: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">银行钞票标签布局测试</h1>
        
        <div class="layout-info">
            <h3>布局信息</h3>
            <p><strong>标签尺寸：</strong>119mm × 30mm（大签标签 - 已优化）</p>
            <p><strong>纸张尺寸：</strong>210mm × 297mm（A4）</p>
            <p><strong>页面边距：</strong>5mm</p>
            <p><strong>标签间距：</strong>2mm</p>
            <p><strong>每行标签数：</strong>1个</p>
            <p><strong>每页行数：</strong>9行</p>
            <p><strong>每页总标签数：</strong>9个</p>
        </div>

        <div class="label-preview">
            <h3>标签预览（实际尺寸缩放）</h3>
            <div class="label" style="width: 119px; height: 30px;">
                <div class="label-content">
                    <div class="bank-name">中国人民银行</div>
                    <div class="coin-info">2023年熊猫金币30克</div>
                    <div class="grade-score">70</div>
                    <div class="grade-level">MS70</div>
                    <div class="stars">★★★★★</div>
                    <div class="serial-number">SN: *************</div>
                    <div class="qr-code">QR</div>
                    <div class="barcode">|||</div>
                </div>
            </div>
        </div>

        <div class="layout-info">
            <h3>优化说明</h3>
            <ul>
                <li>标签高度从26mm增加到30mm，提供更多垂直空间</li>
                <li>调整了各元素的垂直位置，避免重叠</li>
                <li>增加了字体大小，提高可读性</li>
                <li>扩大了二维码和条码的尺寸</li>
                <li>保持了整体布局的美观性和功能性</li>
            </ul>
        </div>

        <div class="layout-info">
            <h3>元素位置详情</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background: #f0f0f0;">
                    <th style="border: 1px solid #ddd; padding: 8px;">元素</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">位置 (top)</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">高度</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">字体大小</th>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">银行名称</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">2mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">8mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">14px</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">钱币信息</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">12mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">7mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">11px</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">评级分数</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">9mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">14mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">26px</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">评级等级</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">21mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">4mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">9px</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">星级</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">25mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">3mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">8px</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">序列号</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">21mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">5mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">9px</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">二维码</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">2mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">18mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">-</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">条码</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">22mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">6mm</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">7px</td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
