// 标签布局计算测试脚本
// 用于验证标签尺寸优化后的布局计算

// 标签尺寸配置（单位：mm）
const labelSizes = {
  1: { width: 191, height: 26 },  // 标准标签
  2: { width: 119, height: 30 },  // 大签标签 - 已优化高度
  3: { width: 160, height: 22 }   // 小标签
};

// 纸张尺寸配置（单位：mm）
const paperSizes = {
  1: { width: 210, height: 297 }, // A4
  2: { width: 210, height: 297 }, // A4
  3: { width: 210, height: 297 }  // A4
};

// 计算标签在纸张上的布局
function calculateLabelLayout(labelSize, paperSize) {
  const margin = 5; // 页面边距 (mm)
  const spacing = 2; // 标签间距 (mm)

  // 可用打印区域
  const printableWidth = paperSize.width - (margin * 2);
  const printableHeight = paperSize.height - (margin * 2);

  // 计算每行可放多少个标签
  const labelsPerRow = Math.floor((printableWidth + spacing) / (labelSize.width + spacing));

  // 计算每列可放多少行标签
  const rowsPerPage = Math.floor((printableHeight + spacing) / (labelSize.height + spacing));

  // 每页总标签数
  const labelsPerPage = labelsPerRow * rowsPerPage;

  return {
    labelsPerRow,
    rowsPerPage,
    labelsPerPage,
    margin,
    spacing,
    printableWidth,
    printableHeight,
    labelSize,
    paperSize
  };
}

// 测试所有标签类型的布局
function testAllLayouts() {
  console.log('=== 标签布局计算测试 ===\n');
  
  for (let labelType = 1; labelType <= 3; labelType++) {
    const labelSize = labelSizes[labelType];
    const paperSize = paperSizes[labelType];
    const layout = calculateLabelLayout(labelSize, paperSize);
    
    console.log(`标签类型 ${labelType}:`);
    console.log(`  标签尺寸: ${labelSize.width}mm × ${labelSize.height}mm`);
    console.log(`  纸张尺寸: ${paperSize.width}mm × ${paperSize.height}mm`);
    console.log(`  可用区域: ${layout.printableWidth}mm × ${layout.printableHeight}mm`);
    console.log(`  布局: ${layout.labelsPerRow}列 × ${layout.rowsPerPage}行`);
    console.log(`  每页标签数: ${layout.labelsPerPage}个`);
    console.log(`  利用率: ${((layout.labelsPerPage * labelSize.width * labelSize.height) / (layout.printableWidth * layout.printableHeight) * 100).toFixed(1)}%`);
    console.log('');
  }
}

// 专门测试大签标签的优化效果
function testBigLabelOptimization() {
  console.log('=== 大签标签优化对比 ===\n');
  
  // 优化前的配置
  const oldLabelSize = { width: 119, height: 26 };
  const newLabelSize = { width: 119, height: 30 };
  const paperSize = { width: 210, height: 297 };
  
  const oldLayout = calculateLabelLayout(oldLabelSize, paperSize);
  const newLayout = calculateLabelLayout(newLabelSize, paperSize);
  
  console.log('优化前 (26mm高度):');
  console.log(`  布局: ${oldLayout.labelsPerRow}列 × ${oldLayout.rowsPerPage}行`);
  console.log(`  每页标签数: ${oldLayout.labelsPerPage}个`);
  console.log(`  利用率: ${((oldLayout.labelsPerPage * oldLabelSize.width * oldLabelSize.height) / (oldLayout.printableWidth * oldLayout.printableHeight) * 100).toFixed(1)}%`);
  
  console.log('\n优化后 (30mm高度):');
  console.log(`  布局: ${newLayout.labelsPerRow}列 × ${newLayout.rowsPerPage}行`);
  console.log(`  每页标签数: ${newLayout.labelsPerPage}个`);
  console.log(`  利用率: ${((newLayout.labelsPerPage * newLabelSize.width * newLabelSize.height) / (newLayout.printableWidth * newLayout.printableHeight) * 100).toFixed(1)}%`);
  
  console.log('\n优化效果:');
  console.log(`  高度增加: ${newLabelSize.height - oldLabelSize.height}mm (+${((newLabelSize.height - oldLabelSize.height) / oldLabelSize.height * 100).toFixed(1)}%)`);
  console.log(`  每页标签数变化: ${newLayout.labelsPerPage - oldLayout.labelsPerPage}个`);
  console.log(`  空间利用率变化: ${(((newLayout.labelsPerPage * newLabelSize.width * newLabelSize.height) / (newLayout.printableWidth * newLayout.printableHeight) * 100) - ((oldLayout.labelsPerPage * oldLabelSize.width * oldLabelSize.height) / (oldLayout.printableWidth * oldLayout.printableHeight) * 100)).toFixed(1)}%`);
}

// 计算元素位置是否合理
function validateElementPositions() {
  console.log('=== 元素位置验证 ===\n');
  
  const labelHeight = 30; // mm
  const elements = [
    { name: '银行名称', top: 2, height: 8 },
    { name: '钱币信息', top: 12, height: 7 },
    { name: '评级分数', top: 9, height: 14 },
    { name: '评级等级', top: 21, height: 4 },
    { name: '星级', top: 25, height: 3 },
    { name: '序列号', top: 21, height: 5 },
    { name: '二维码', top: 2, height: 18 },
    { name: '条码', top: 22, height: 6 }
  ];
  
  console.log(`标签总高度: ${labelHeight}mm\n`);
  
  let hasOverflow = false;
  elements.forEach(element => {
    const bottom = element.top + element.height;
    const isOverflow = bottom > labelHeight;
    
    console.log(`${element.name}:`);
    console.log(`  位置: ${element.top}mm - ${bottom}mm`);
    console.log(`  状态: ${isOverflow ? '❌ 超出边界' : '✅ 正常'}`);
    
    if (isOverflow) {
      hasOverflow = true;
      console.log(`  超出: ${bottom - labelHeight}mm`);
    }
    console.log('');
  });
  
  console.log(`总体验证: ${hasOverflow ? '❌ 存在元素超出标签边界' : '✅ 所有元素位置正常'}`);
}

// 运行所有测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = {
    calculateLabelLayout,
    testAllLayouts,
    testBigLabelOptimization,
    validateElementPositions
  };
} else {
  // 浏览器环境
  testAllLayouts();
  testBigLabelOptimization();
  validateElementPositions();
}
